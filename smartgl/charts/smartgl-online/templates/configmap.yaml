{{- if .Values.configMap.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "smartgl-online.fullname" . }}-configmap
  namespace: {{ .Values.global.namespace | default "default" }}
  labels:
    app.kubernetes.io/name: {{ include "smartgl-online.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: smartgl-online
    app.kubernetes.io/managed-by: {{ .Release.Service }}
data:
  {{- range $key, $value := .Values.configMap.data }}
  {{ $key }}: |
    {{- tpl $value $ | nindent 4 }}
  {{- end }}
{{- end }} 