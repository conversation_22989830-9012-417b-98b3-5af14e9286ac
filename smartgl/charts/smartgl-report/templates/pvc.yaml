{{- if .Values.persistentVolume.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "smartgl-report.fullname" . }}-pvc
  namespace: {{ .Values.global.namespace }}
  labels:
    app.kubernetes.io/name: {{ include "smartgl-report.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  accessModes:
    - {{ .Values.persistentVolume.accessMode | default "ReadWriteOnce" }}
  resources:
    requests:
      storage: {{ .Values.persistentVolume.size | default "1Gi" }}
  {{- if .Values.persistentVolume.storageClass }}
  storageClassName: {{ .Values.persistentVolume.storageClass }}
  {{- end }}
{{- end }} 