apiVersion: v1
kind: Service
metadata:
  name: {{ include "smartgl-report.fullname" . }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "smartgl-report.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "smartgl-report.selectorLabels" . | nindent 4 }}
