{{- if .Values.configMap.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "smartgl-manager.fullname" . }}-configmap
  namespace: {{ .Values.global.namespace | default "default" }}
  labels:
    app.kubernetes.io/name: {{ include "smartgl-manager.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: smartgl-manager
    app.kubernetes.io/managed-by: {{ .Release.Service }}
data:
  {{- range $key, $value := .Values.configMap.data }}
  {{ $key }}: |
    {{- tpl $value $ | nindent 4 }}
  {{- end }}
{{- end }} 