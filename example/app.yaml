---
# Source: parent-chart/charts/service-a/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: parent-chart-service-a
  labels:
    helm.sh/chart: service-a-0.1.0
    app.kubernetes.io/name: service-a
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
automountServiceAccountToken: true
---
# Source: parent-chart/charts/service-b/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: parent-chart-service-b
  labels:
    helm.sh/chart: service-b-0.1.0
    app.kubernetes.io/name: service-b
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
automountServiceAccountToken: true
---
# Source: parent-chart/charts/service-c/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: parent-chart-service-c
  labels:
    helm.sh/chart: service-c-0.1.0
    app.kubernetes.io/name: service-c
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
automountServiceAccountToken: true
---
# Source: parent-chart/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: parent-chart
  labels:
    helm.sh/chart: parent-chart-0.1.0
    app.kubernetes.io/name: parent-chart
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
automountServiceAccountToken: true
---
# Source: parent-chart/charts/service-a/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: parent-chart-service-a
  labels:
    helm.sh/chart: service-a-0.1.0
    app.kubernetes.io/name: service-a
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: service-a
    app.kubernetes.io/instance: parent-chart
---
# Source: parent-chart/charts/service-b/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: parent-chart-service-b
  labels:
    helm.sh/chart: service-b-0.1.0
    app.kubernetes.io/name: service-b
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: service-b
    app.kubernetes.io/instance: parent-chart
---
# Source: parent-chart/charts/service-c/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: parent-chart-service-c
  labels:
    helm.sh/chart: service-c-0.1.0
    app.kubernetes.io/name: service-c
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: service-c
    app.kubernetes.io/instance: parent-chart
---
# Source: parent-chart/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: parent-chart
  labels:
    helm.sh/chart: parent-chart-0.1.0
    app.kubernetes.io/name: parent-chart
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: parent-chart
    app.kubernetes.io/instance: parent-chart
---
# Source: parent-chart/charts/service-a/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: parent-chart-service-a
  labels:
    helm.sh/chart: service-a-0.1.0
    app.kubernetes.io/name: service-a
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: service-a
      app.kubernetes.io/instance: parent-chart
  template:
    metadata:
      labels:
        helm.sh/chart: service-a-0.1.0
        app.kubernetes.io/name: service-a
        app.kubernetes.io/instance: parent-chart
        app.kubernetes.io/version: "1.16.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      serviceAccountName: parent-chart-service-a
      securityContext:
        {}
      containers:
        - name: service-a
          securityContext:
            {}
          image: "nginx:1.16.0"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /
              port: http
          resources:
            {}
---
# Source: parent-chart/charts/service-b/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: parent-chart-service-b
  labels:
    helm.sh/chart: service-b-0.1.0
    app.kubernetes.io/name: service-b
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: service-b
      app.kubernetes.io/instance: parent-chart
  template:
    metadata:
      labels:
        helm.sh/chart: service-b-0.1.0
        app.kubernetes.io/name: service-b
        app.kubernetes.io/instance: parent-chart
        app.kubernetes.io/version: "1.16.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      serviceAccountName: parent-chart-service-b
      securityContext:
        {}
      containers:
        - name: service-b
          securityContext:
            {}
          image: "nginx:1.16.0"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /
              port: http
          resources:
            {}
---
# Source: parent-chart/charts/service-c/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: parent-chart-service-c
  labels:
    helm.sh/chart: service-c-0.1.0
    app.kubernetes.io/name: service-c
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: service-c
      app.kubernetes.io/instance: parent-chart
  template:
    metadata:
      labels:
        helm.sh/chart: service-c-0.1.0
        app.kubernetes.io/name: service-c
        app.kubernetes.io/instance: parent-chart
        app.kubernetes.io/version: "1.16.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      serviceAccountName: parent-chart-service-c
      securityContext:
        {}
      containers:
        - name: service-c
          securityContext:
            {}
          image: "nginx:1.16.0"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /
              port: http
          resources:
            {}
---
# Source: parent-chart/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: parent-chart
  labels:
    helm.sh/chart: parent-chart-0.1.0
    app.kubernetes.io/name: parent-chart
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: parent-chart
      app.kubernetes.io/instance: parent-chart
  template:
    metadata:
      labels:
        helm.sh/chart: parent-chart-0.1.0
        app.kubernetes.io/name: parent-chart
        app.kubernetes.io/instance: parent-chart
        app.kubernetes.io/version: "1.16.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      serviceAccountName: parent-chart
      securityContext:
        {}
      containers:
        - name: parent-chart
          securityContext:
            {}
          image: "nginx:1.16.0"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /
              port: http
          resources:
            {}
---
# Source: parent-chart/charts/service-a/templates/tests/test-connection.yaml
apiVersion: v1
kind: Pod
metadata:
  name: "parent-chart-service-a-test-connection"
  labels:
    helm.sh/chart: service-a-0.1.0
    app.kubernetes.io/name: service-a
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['parent-chart-service-a:80']
  restartPolicy: Never
---
# Source: parent-chart/charts/service-b/templates/tests/test-connection.yaml
apiVersion: v1
kind: Pod
metadata:
  name: "parent-chart-service-b-test-connection"
  labels:
    helm.sh/chart: service-b-0.1.0
    app.kubernetes.io/name: service-b
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['parent-chart-service-b:80']
  restartPolicy: Never
---
# Source: parent-chart/charts/service-c/templates/tests/test-connection.yaml
apiVersion: v1
kind: Pod
metadata:
  name: "parent-chart-service-c-test-connection"
  labels:
    helm.sh/chart: service-c-0.1.0
    app.kubernetes.io/name: service-c
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['parent-chart-service-c:80']
  restartPolicy: Never
---
# Source: parent-chart/templates/tests/test-connection.yaml
apiVersion: v1
kind: Pod
metadata:
  name: "parent-chart-test-connection"
  labels:
    helm.sh/chart: parent-chart-0.1.0
    app.kubernetes.io/name: parent-chart
    app.kubernetes.io/instance: parent-chart
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['parent-chart:80']
  restartPolicy: Never
